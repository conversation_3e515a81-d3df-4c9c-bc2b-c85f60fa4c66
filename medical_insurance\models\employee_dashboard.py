# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from datetime import date
import calendar


class EmployeeDashboard(models.TransientModel):
    _name = 'employee.dashboard'
    _description = 'Employee Medical Dashboard'
    _rec_name = 'employee_name'

    employee_id = fields.Many2one('hr.employee', string='Employee', required=True)
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)
    company_currency_id = fields.Many2one(related='company_id.currency_id', string='Currency', readonly=True)

    # Employee Information
    employee_image = fields.Binary(related='employee_id.image_1920', string='Employee Image', readonly=True)
    employee_name = fields.Char(related='employee_id.name', string='Name', readonly=True)
    employee_job = fields.Char(string='Job Title', compute='_compute_employee_job')
    employee_department = fields.Many2one(related='employee_id.department_id', string='Department', readonly=True)
    employee_work_phone = fields.Char(string='Work Phone', compute='_compute_employee_contact')
    employee_work_email = fields.Char(string='Work Email', compute='_compute_employee_contact')
    employee_mobile = fields.Char(string='Mobile', compute='_compute_employee_contact')
    employee_number = fields.Char(string='Employee ID', compute='_compute_employee_number')
    employee_marital = fields.Selection(related='employee_id.marital', string='Marital Status', readonly=True)
    employee_age = fields.Integer(string='Age', compute='_compute_employee_age')
    employee_blood_type = fields.Char(string='Blood Type', compute='_compute_employee_blood_type')

    # Family Members
    family_member_ids = fields.One2many(related='employee_id.family_member_ids', string='Family Members', readonly=True)
    family_member_count = fields.Integer(related='employee_id.family_member_count', string='Family Member Count', readonly=True)

    # Medical Facilities
    top_facilities_ids = fields.Many2many('medical.facility', string='Top Medical Facilities', compute='_compute_top_facilities')

    # Cap Information
    employee_cap = fields.Float(string='Employee Cap', compute='_compute_employee_cap')
    previous_claims_amount = fields.Float(string='Previous Claims Amount', compute='_compute_previous_claims')
    remaining_balance = fields.Float(string='Remaining Balance', compute='_compute_remaining_balance')
    reimbursement_rate = fields.Float(string='Reimbursement Rate', compute='_compute_reimbursement_rate')

    # Claims Statistics
    total_claims_count = fields.Integer(string='Total Claims', compute='_compute_claims_statistics')
    approved_claims_count = fields.Integer(string='Approved Claims', compute='_compute_claims_statistics')
    pending_claims_count = fields.Integer(string='Pending Claims', compute='_compute_claims_statistics')
    rejected_claims_count = fields.Integer(string='Rejected Claims', compute='_compute_claims_statistics')

    # Dashboard data
    claims_by_state_graph = fields.Text(string='Claims by State', compute='_compute_claims_by_state_graph')
    claims_by_type_graph = fields.Text(string='Claims by Type', compute='_compute_claims_by_type_graph')
    monthly_claims_graph = fields.Text(string='Monthly Claims', compute='_compute_monthly_claims_graph')
    recent_claims_ids = fields.Many2many('medical.claim', string='Recent Claims', compute='_compute_recent_claims')

    @api.model
    def default_get(self, fields_list):
        """Set default employee to current user's employee"""
        res = super(EmployeeDashboard, self).default_get(fields_list)

        # Get the current user's employee
        employee = self.env['hr.employee'].search([('user_id', '=', self.env.user.id)], limit=1)
        if employee:
            res['employee_id'] = employee.id

        return res

    @api.model
    def get_dashboard_data(self):
        """Get or create a dashboard record for the current user"""
        # Get the current user's employee
        employee = self.env['hr.employee'].search([('user_id', '=', self.env.user.id)], limit=1)
        if not employee:
            return False

        # First, delete any existing dashboard records for this employee
        existing_dashboards = self.search([('employee_id', '=', employee.id)])
        if existing_dashboards:
            existing_dashboards.unlink()

        # Create a new dashboard record
        dashboard = self.create({'employee_id': employee.id})

        # Refresh the dashboard data
        dashboard._compute_employee_job()
        dashboard._compute_employee_contact()
        dashboard._compute_employee_number()
        dashboard._compute_employee_blood_type()
        dashboard._compute_employee_age()
        dashboard._compute_top_facilities()
        dashboard._compute_claims_statistics()
        dashboard._compute_employee_cap()
        dashboard._compute_previous_claims()
        dashboard._compute_remaining_balance()
        dashboard._compute_reimbursement_rate()
        dashboard._compute_claims_by_state_graph()
        dashboard._compute_claims_by_type_graph()
        dashboard._compute_monthly_claims_graph()
        dashboard._compute_recent_claims()

        # Return the dashboard record ID
        return dashboard.id



    def _compute_employee_job(self):
        """Compute employee job title"""
        for record in self:
            if record.employee_id:
                if hasattr(record.employee_id, 'job_title') and record.employee_id.job_title:
                    record.employee_job = record.employee_id.job_title
                elif hasattr(record.employee_id, 'job_id') and record.employee_id.job_id:
                    record.employee_job = record.employee_id.job_id.name
                else:
                    record.employee_job = ''
            else:
                record.employee_job = ''

    def _compute_employee_contact(self):
        """Compute employee contact information"""
        for record in self:
            # Work phone
            if record.employee_id and hasattr(record.employee_id, 'work_phone'):
                record.employee_work_phone = record.employee_id.work_phone or ''
            else:
                record.employee_work_phone = ''

            # Work email
            if record.employee_id and hasattr(record.employee_id, 'work_email'):
                record.employee_work_email = record.employee_id.work_email or ''
            else:
                record.employee_work_email = ''

            # Mobile phone
            if record.employee_id and hasattr(record.employee_id, 'mobile_phone'):
                record.employee_mobile = record.employee_id.mobile_phone or ''
            else:
                record.employee_mobile = ''

    def _compute_employee_number(self):
        """Compute employee number"""
        for record in self:
            if record.employee_id:
                if hasattr(record.employee_id, 'int_id') and record.employee_id.int_id:
                    record.employee_number = record.employee_id.int_id
                else:
                    record.employee_number = f"EMP{record.employee_id.id:05d}" if record.employee_id.id else ""
            else:
                record.employee_number = ""

    def _compute_employee_blood_type(self):
        """Compute employee blood type"""
        for record in self:
            if record.employee_id and hasattr(record.employee_id, 'bloodtype'):
                blood_type_map = {
                    '1': 'A+', '2': 'A-', '3': 'B+', '4': 'B-',
                    '5': 'AB+', '6': 'AB-', '7': 'O+', '8': 'O-'
                }
                record.employee_blood_type = blood_type_map.get(record.employee_id.bloodtype, '')
            else:
                record.employee_blood_type = ''

    def _compute_employee_age(self):
        """Compute employee age based on birth date"""
        for record in self:
            # تجنب الوصول إلى حقل birthday إذا لم يكن المستخدم يملك الصلاحيات المناسبة
            try:
                if record.employee_id and hasattr(record.employee_id, 'birthday') and record.employee_id.birthday:
                    today = date.today()
                    born = record.employee_id.birthday
                    record.employee_age = today.year - born.year - ((today.month, today.day) < (born.month, born.day))
                else:
                    record.employee_age = 0
            except Exception:
                # في حالة حدوث خطأ في الوصول، نضع قيمة افتراضية
                record.employee_age = 0

    def _compute_top_facilities(self):
        """Compute top medical facilities used by the employee"""
        for record in self:
            if not record.employee_id:
                record.top_facilities_ids = [(6, 0, [])]
                continue

            # Get all claims for the employee
            domain = [('employee_id', '=', record.employee_id.id)]

            # Get facilities grouped by count
            facilities_data = self.env['medical.claim'].read_group(
                domain=domain,
                fields=['facility_id'],
                groupby=['facility_id'],
                limit=5
            )

            facility_ids = [data['facility_id'][0] for data in facilities_data if data['facility_id']]
            record.top_facilities_ids = [(6, 0, facility_ids)]

    def _compute_claims_statistics(self):
        """Compute claims statistics"""
        for record in self:
            if not record.employee_id:
                record.total_claims_count = 0
                record.approved_claims_count = 0
                record.pending_claims_count = 0
                record.rejected_claims_count = 0
                continue

            # Get all claims for the employee
            domain = [('employee_id', '=', record.employee_id.id)]
            record.total_claims_count = self.env['medical.claim'].search_count(domain)

            # Approved claims (approved + paid)
            approved_domain = domain + [('state', 'in', ['approved', 'paid'])]
            record.approved_claims_count = self.env['medical.claim'].search_count(approved_domain)

            # Pending claims (draft + submitted)
            pending_domain = domain + [('state', 'in', ['draft', 'submitted'])]
            record.pending_claims_count = self.env['medical.claim'].search_count(pending_domain)

            # Rejected claims
            rejected_domain = domain + [('state', '=', 'rejected')]
            record.rejected_claims_count = self.env['medical.claim'].search_count(rejected_domain)

    def _compute_employee_cap(self):
        """Compute employee cap based on marital status"""
        for record in self:
            if not record.employee_id:
                record.employee_cap = 0.0
                continue

            # Get insurance settings
            config = self.env['medical.insurance.config'].search([], limit=1)

            try:
                # Determine cap based on marital status
                if record.employee_id.marital == 'single':
                    record.employee_cap = config.single_cap
                elif record.employee_id.marital in ['married', 'married_and_provide']:
                    # Treat married employees the same as married with dependents
                    record.employee_cap = config.married_with_dependents_cap
                else:
                    record.employee_cap = config.single_cap  # Default to single cap
            except Exception:
                # في حالة حدوث خطأ في الوصول إلى حقل marital، نستخدم القيمة الافتراضية
                record.employee_cap = config.married_with_dependents_cap  # استخدم القيمة الأعلى كافتراضي

    def _compute_previous_claims(self):
        """Compute previous claims amount for the employee"""
        for record in self:
            if not record.employee_id:
                record.previous_claims_amount = 0.0
                continue

            # Get current year
            current_year = date.today().year
            start_date = date(current_year, 1, 1)
            end_date = date(current_year, 12, 31)

            # Get all approved and paid claims for the employee in the current year
            domain = [
                ('employee_id', '=', record.employee_id.id),
                ('date', '>=', start_date),
                ('date', '<=', end_date),
                ('state', 'in', ['approved', 'paid'])
            ]

            claims = self.env['medical.claim'].search(domain)
            record.previous_claims_amount = sum(claims.mapped('reimbursed_amount'))

    def _compute_remaining_balance(self):
        """Compute remaining balance for the employee"""
        for record in self:
            record.remaining_balance = record.employee_cap - record.previous_claims_amount
            if record.remaining_balance < 0:
                record.remaining_balance = 0

    def _compute_reimbursement_rate(self):
        """Compute reimbursement rate from config"""
        for record in self:
            config = self.env['medical.insurance.config'].search([], limit=1)
            # Store as a decimal for the percentage widget (80.0 -> 0.8)
            record.reimbursement_rate = config.reimbursement_rate / 100.0

    def _compute_claims_by_state_graph(self):
        """Compute claims by state graph data"""
        for record in self:
            if not record.employee_id:
                record.claims_by_state_graph = "لا توجد بيانات"
                continue

            employee_id = record.employee_id.id
            domain = [('employee_id', '=', employee_id)]

            # Get claims grouped by state
            claims_by_state = self.env['medical.claim'].read_group(
                domain=domain,
                fields=['state', 'total_amount'],
                groupby=['state']
            )

            # Prepare data for text display
            state_labels = {
                'draft': 'مسودة',
                'submitted': 'مقدمة',
                'approved': 'موافق عليها',
                'rejected': 'مرفوضة',
                'paid': 'مدفوعة'
            }

            result = []
            for group in claims_by_state:
                state = group['state']
                count = group['state_count']
                amount = group['total_amount']
                if count > 0:
                    result.append(f"{state_labels.get(state, state)}: {count} ({amount:.2f})")

            record.claims_by_state_graph = "\n".join(result) if result else "لا توجد بيانات"



    def _compute_claims_by_type_graph(self):
        """Compute claims by type graph data"""
        for record in self:
            if not record.employee_id:
                record.claims_by_type_graph = "لا توجد بيانات"
                continue

            employee_id = record.employee_id.id
            domain = [('employee_id', '=', employee_id)]

            # Get claims grouped by type
            claims_by_type = self.env['medical.claim'].read_group(
                domain=domain,
                fields=['claim_type', 'total_amount'],
                groupby=['claim_type']
            )

            # Prepare data for text display
            claim_type_field = self.env['medical.claim']._fields['claim_type']
            type_labels = dict(claim_type_field.selection)

            result = []
            for group in claims_by_type:
                claim_type = group['claim_type']
                count = group['claim_type_count']
                amount = group['total_amount']
                if count > 0:
                    result.append(f"{type_labels.get(claim_type, claim_type)}: {count} ({amount:.2f})")

            record.claims_by_type_graph = "\n".join(result) if result else "لا توجد بيانات"

    def _compute_monthly_claims_graph(self):
        """Compute monthly claims graph data"""
        for record in self:
            if not record.employee_id:
                record.monthly_claims_graph = "لا توجد بيانات"
                continue

            employee_id = record.employee_id.id
            current_year = date.today().year

            # Add month name in Arabic
            month_names = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ]

            result = []
            has_data = False

            # Get data for each month
            for month in range(1, 13):
                start_date = date(current_year, month, 1)
                last_day = calendar.monthrange(current_year, month)[1]
                end_date = date(current_year, month, last_day)

                domain = [
                    ('employee_id', '=', employee_id),
                    ('date', '>=', start_date),
                    ('date', '<=', end_date)
                ]

                claims = self.env['medical.claim'].search(domain)
                total_amount = sum(claims.mapped('total_amount'))
                reimbursed_amount = sum(claims.mapped('reimbursed_amount'))

                if total_amount > 0:
                    has_data = True
                    result.append(f"{month_names[month-1]}: المبلغ الإجمالي: {total_amount:.2f}, المبلغ المسترد: {reimbursed_amount:.2f}")

            record.monthly_claims_graph = "\n".join(result) if has_data else "لا توجد بيانات"

    def _compute_recent_claims(self):
        """Compute recent claims"""
        for record in self:
            if not record.employee_id:
                record.recent_claims_ids = [(6, 0, [])]
                continue

            employee_id = record.employee_id.id
            domain = [('employee_id', '=', employee_id)]

            # Get all claims for the employee, ordered by date and ID
            claims = self.env['medical.claim'].search(domain, order='date desc, id desc', limit=10)
            if claims:
                record.recent_claims_ids = [(6, 0, claims.ids)]
            else:
                record.recent_claims_ids = [(6, 0, [])]

    def action_view_all_claims(self):
        """View all claims for the employee"""
        self.ensure_one()
        action = self.env.ref('medical_insurance.action_my_medical_claim').read()[0]
        return action

    def action_view_mode(self):
        """Switch to view mode after loading data"""
        self.ensure_one()

        # Return a client action to reload the view in readonly mode
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'employee.dashboard',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'current',
            'context': {'form_view_ref': 'readonly'},
            'flags': {'mode': 'readonly'},
        }

    def action_refresh_dashboard(self):
        """Refresh dashboard by recomputing all fields"""
        self.ensure_one()

        # Recompute all computed fields
        self._compute_employee_cap()
        self._compute_previous_claims()
        self._compute_remaining_balance()
        self._compute_reimbursement_rate()
        self._compute_claims_by_state_graph()
        self._compute_claims_by_type_graph()
        self._compute_monthly_claims_graph()
        self._compute_recent_claims()

        # Return a client action to reload the view without creating a new record
        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
        }
