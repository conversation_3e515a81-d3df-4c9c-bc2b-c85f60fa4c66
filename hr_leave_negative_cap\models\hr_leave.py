# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class HrLeave(models.Model):
    _inherit = 'hr.leave'

    # Computed fields for negative cap information
    allow_negative_cap = fields.Boolean(
        related='holiday_status_id.allow_negative_cap',
        string='Allow Negative Cap',
        readonly=True
    )

    negative_cap_limit = fields.Float(
        related='holiday_status_id.negative_cap_limit',
        string='Negative Cap Limit',
        readonly=True
    )

    def _check_holidays(self):
        """Override the main validation method"""
        # For negative cap enabled types, use custom validation
        negative_cap_leaves = self.filtered(lambda l: l.holiday_status_id.allow_negative_cap)
        regular_leaves = self - negative_cap_leaves

        # Handle negative cap leaves with custom validation
        for leave in negative_cap_leaves:
            if (leave.holiday_type == 'employee' and leave.employee_id and
                leave.holiday_status_id.requires_allocation == 'yes'):

                # Get current balance
                leave_days = leave.holiday_status_id.get_employees_days([leave.employee_id.id])
                if (leave.holiday_status_id.id in leave_days and
                    leave.employee_id.id in leave_days[leave.holiday_status_id.id]):

                    remaining_leaves = leave_days[leave.holiday_status_id.id][leave.employee_id.id]['remaining_leaves']
                    balance_after = remaining_leaves - leave.number_of_days

                    # Check negative cap limit
                    if balance_after < -leave.holiday_status_id.negative_cap_limit:
                        raise ValidationError(_(
                            'You cannot exceed the negative cap limit of %.2f days. '
                            'Current balance: %.2f days, Requested: %.2f days, '
                            'Balance after would be: %.2f days'
                        ) % (
                            leave.holiday_status_id.negative_cap_limit,
                            remaining_leaves,
                            leave.number_of_days,
                            balance_after
                        ))

        # Handle regular leaves with original validation
        if regular_leaves:
            super(HrLeave, regular_leaves)._check_holidays()




