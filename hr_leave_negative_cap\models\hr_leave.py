# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class HrLeave(models.Model):
    _inherit = 'hr.leave'

    # Computed fields for negative cap information
    allow_negative_cap = fields.Boolean(
        related='holiday_status_id.allow_negative_cap',
        string='Allow Negative Cap',
        readonly=True
    )
    
    negative_cap_limit = fields.Float(
        related='holiday_status_id.negative_cap_limit',
        string='Negative Cap Limit',
        readonly=True
    )

    def _check_leave_type_validity(self):
        """Override the original method to include negative cap logic"""
        for leave in self:
            if leave.state in ('draft', 'confirm', 'validate1') and leave.holiday_status_id and leave.employee_id:
                if leave.holiday_status_id.requires_allocation == 'yes':
                    # Get current leave balance
                    leave_days = leave.holiday_status_id.get_employees_days([leave.employee_id.id])

                    if leave.holiday_status_id.id in leave_days and leave.employee_id.id in leave_days[leave.holiday_status_id.id]:
                        current_balance = leave_days[leave.holiday_status_id.id][leave.employee_id.id]['remaining_leaves']

                        # Calculate balance after this leave (excluding current leave if updating)
                        other_leaves = self.env['hr.leave'].search([
                            ('employee_id', '=', leave.employee_id.id),
                            ('holiday_status_id', '=', leave.holiday_status_id.id),
                            ('state', 'in', ['validate', 'validate1']),
                            ('id', '!=', leave.id)
                        ])
                        used_days = sum(other_leaves.mapped('number_of_days'))
                        available_balance = current_balance + used_days
                        balance_after_leave = available_balance - leave.number_of_days

                        # Check if negative cap is allowed
                        if not leave.holiday_status_id.allow_negative_cap and balance_after_leave < 0:
                            raise ValidationError(_(
                                'You cannot take more leave than your available balance. '
                                'Available balance: %.2f days, Requested: %.2f days'
                            ) % (available_balance, leave.number_of_days))

                        # Check negative cap limit
                        elif leave.holiday_status_id.allow_negative_cap and balance_after_leave < -leave.holiday_status_id.negative_cap_limit:
                            raise ValidationError(_(
                                'You cannot exceed the negative cap limit. '
                                'Maximum negative balance allowed: %.2f days, '
                                'Balance after this leave would be: %.2f days'
                            ) % (leave.holiday_status_id.negative_cap_limit, balance_after_leave))

        # Call the original method for other validations, but skip balance check
        # We handle balance check ourselves above
        pass

    def _check_holidays(self):
        """Override the original validation to handle negative cap"""
        for holiday in self:
            if holiday.holiday_type != 'employee' or not holiday.employee_id or holiday.holiday_status_id.allocation_type == 'no':
                continue

            # If negative cap is enabled, use our custom validation
            if holiday.holiday_status_id.allow_negative_cap:
                # Get current balance
                leave_days = holiday.holiday_status_id.get_employees_days([holiday.employee_id.id])
                if holiday.holiday_status_id.id in leave_days and holiday.employee_id.id in leave_days[holiday.holiday_status_id.id]:
                    remaining_leaves = leave_days[holiday.holiday_status_id.id][holiday.employee_id.id]['remaining_leaves']

                    # Calculate balance after this leave
                    balance_after = remaining_leaves - holiday.number_of_days

                    # Only check if we exceed the negative cap limit
                    if balance_after < -holiday.holiday_status_id.negative_cap_limit:
                        raise ValidationError(_(
                            'You cannot exceed the negative cap limit of %.2f days. '
                            'Current balance: %.2f days, Requested: %.2f days, '
                            'Balance after would be: %.2f days'
                        ) % (
                            holiday.holiday_status_id.negative_cap_limit,
                            remaining_leaves,
                            holiday.number_of_days,
                            balance_after
                        ))
                # Skip the original validation for negative cap enabled types
                continue

            # For non-negative cap types, use original validation
            super(HrLeave, self)._check_holidays()

    @api.constrains('state', 'number_of_days', 'holiday_status_id', 'employee_id')
    def _check_leave_type_validity(self):
        """Override to handle negative cap validation"""
        for leave in self:
            if (leave.state in ('draft', 'confirm', 'validate1', 'validate') and
                leave.holiday_type == 'employee' and
                leave.employee_id and
                leave.holiday_status_id and
                leave.holiday_status_id.requires_allocation == 'yes'):

                # If negative cap is enabled, use custom validation
                if leave.holiday_status_id.allow_negative_cap:
                    # Get current balance
                    leave_days = leave.holiday_status_id.get_employees_days([leave.employee_id.id])
                    if (leave.holiday_status_id.id in leave_days and
                        leave.employee_id.id in leave_days[leave.holiday_status_id.id]):

                        remaining_leaves = leave_days[leave.holiday_status_id.id][leave.employee_id.id]['remaining_leaves']
                        balance_after = remaining_leaves - leave.number_of_days

                        # Only check negative cap limit
                        if balance_after < -leave.holiday_status_id.negative_cap_limit:
                            raise ValidationError(_(
                                'You cannot exceed the negative cap limit of %.2f days. '
                                'Current balance: %.2f days, Requested: %.2f days'
                            ) % (
                                leave.holiday_status_id.negative_cap_limit,
                                remaining_leaves,
                                leave.number_of_days
                            ))
                    # Skip original validation for negative cap types
                    continue

                # For non-negative cap types, use original validation
                super(HrLeave, self)._check_leave_type_validity()

    @api.model
    def create(self, vals):
        """Override create to handle negative cap validation"""
        leave = super(HrLeave, self).create(vals)

        # If this is a negative cap enabled leave type, skip standard validation
        if (leave.holiday_status_id and leave.holiday_status_id.allow_negative_cap and
            leave.holiday_type == 'employee' and leave.employee_id):
            # Custom validation for negative cap
            leave._validate_negative_cap()

        return leave

    def write(self, vals):
        """Override write to handle negative cap validation"""
        result = super(HrLeave, self).write(vals)

        # If this is a negative cap enabled leave type, skip standard validation
        for leave in self:
            if (leave.holiday_status_id and leave.holiday_status_id.allow_negative_cap and
                leave.holiday_type == 'employee' and leave.employee_id):
                # Custom validation for negative cap
                leave._validate_negative_cap()

        return result

    def _validate_negative_cap(self):
        """Custom validation for negative cap enabled leave types"""
        self.ensure_one()

        if (self.holiday_status_id.requires_allocation == 'yes' and
            self.state in ('confirm', 'validate1', 'validate')):

            # Get current balance
            leave_days = self.holiday_status_id.get_employees_days([self.employee_id.id])
            if (self.holiday_status_id.id in leave_days and
                self.employee_id.id in leave_days[self.holiday_status_id.id]):

                remaining_leaves = leave_days[self.holiday_status_id.id][self.employee_id.id]['remaining_leaves']
                balance_after = remaining_leaves - self.number_of_days

                # Check negative cap limit
                if balance_after < -self.holiday_status_id.negative_cap_limit:
                    raise ValidationError(_(
                        'You cannot exceed the negative cap limit of %.2f days. '
                        'Current balance: %.2f days, Requested: %.2f days, '
                        'Balance after would be: %.2f days'
                    ) % (
                        self.holiday_status_id.negative_cap_limit,
                        remaining_leaves,
                        self.number_of_days,
                        balance_after
                    ))

    @api.constrains('holiday_status_id', 'date_from', 'date_to', 'employee_id')
    def _check_date_state(self):
        """Override to skip original validation for negative cap types"""
        # Filter out negative cap enabled leaves from original validation
        leaves_to_check = self.filtered(lambda l: not (l.holiday_status_id and l.holiday_status_id.allow_negative_cap))

        if leaves_to_check:
            # Call original validation only for non-negative cap leaves
            super(HrLeave, leaves_to_check)._check_date_state()

    def action_confirm(self):
        """Override to handle negative cap validation on confirm"""
        # For negative cap enabled types, use custom validation
        for leave in self:
            if (leave.holiday_status_id and leave.holiday_status_id.allow_negative_cap and
                leave.holiday_type == 'employee' and leave.employee_id):
                leave._validate_negative_cap()

        return super(HrLeave, self).action_confirm()

    def action_approve(self):
        """Override to handle negative cap validation on approve"""
        # For negative cap enabled types, use custom validation
        for leave in self:
            if (leave.holiday_status_id and leave.holiday_status_id.allow_negative_cap and
                leave.holiday_type == 'employee' and leave.employee_id):
                leave._validate_negative_cap()

        return super(HrLeave, self).action_approve()

    @api.depends('holiday_status_id', 'employee_id')
    def _compute_number_of_days_display(self):
        """Override to show correct balance including negative values"""
        super(HrLeave, self)._compute_number_of_days_display()

        for leave in self:
            if (leave.holiday_status_id and leave.employee_id and
                leave.holiday_status_id.allow_negative_cap and
                leave.holiday_status_id.requires_allocation == 'yes'):

                # Get real balance including negative values
                leave_days = leave.holiday_status_id.get_employees_days([leave.employee_id.id])
                if (leave.holiday_status_id.id in leave_days and
                    leave.employee_id.id in leave_days[leave.holiday_status_id.id]):

                    # Ensure balance calculation is triggered for negative cap types
                    leave.number_of_days_display = leave.number_of_days

    def _get_number_of_days(self, date_from, date_to, employee_id):
        """Override to handle negative cap calculations"""
        result = super(HrLeave, self)._get_number_of_days(date_from, date_to, employee_id)
        return result

    @api.model
    def _get_leave_balance_info(self, employee_id, leave_type_id, date=None):
        """Get detailed leave balance information including negative cap"""
        if not date:
            date = fields.Date.today()
        
        leave_type = self.env['hr.leave.type'].browse(leave_type_id)
        leave_days = leave_type.get_employees_days([employee_id], date)
        
        if leave_type_id in leave_days and employee_id in leave_days[leave_type_id]:
            balance_info = leave_days[leave_type_id][employee_id]
            
            # Add negative cap information
            balance_info.update({
                'allow_negative_cap': leave_type.allow_negative_cap,
                'negative_cap_limit': leave_type.negative_cap_limit,
                'min_allowed_balance': -leave_type.negative_cap_limit if leave_type.allow_negative_cap else 0.0,
                'can_go_negative': leave_type.allow_negative_cap,
            })
            
            return balance_info
        
        return {
            'remaining_leaves': 0.0,
            'allow_negative_cap': leave_type.allow_negative_cap,
            'negative_cap_limit': leave_type.negative_cap_limit,
            'min_allowed_balance': -leave_type.negative_cap_limit if leave_type.allow_negative_cap else 0.0,
            'can_go_negative': leave_type.allow_negative_cap,
        }
