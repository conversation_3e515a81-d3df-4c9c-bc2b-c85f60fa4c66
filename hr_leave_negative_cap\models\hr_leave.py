# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class HrLeave(models.Model):
    _inherit = 'hr.leave'

    # Computed fields for negative cap information
    allow_negative_cap = fields.Boolean(
        related='holiday_status_id.allow_negative_cap',
        string='Allow Negative Cap',
        readonly=True
    )
    
    negative_cap_limit = fields.Float(
        related='holiday_status_id.negative_cap_limit',
        string='Negative Cap Limit',
        readonly=True
    )

    @api.constrains('holiday_status_id', 'employee_id', 'date_from', 'date_to', 'number_of_days')
    def _check_negative_cap_limit(self):
        """Check if the leave request exceeds the negative cap limit"""
        for leave in self:
            if leave.holiday_status_id and leave.employee_id and leave.request_date_from and leave.number_of_days > 0:
                # Get current leave balance
                leave_days = leave.holiday_status_id.get_employees_days([leave.employee_id.id], leave.request_date_from)
                
                if leave.holiday_status_id.id in leave_days and leave.employee_id.id in leave_days[leave.holiday_status_id.id]:
                    current_balance = leave_days[leave.holiday_status_id.id][leave.employee_id.id]['remaining_leaves']
                    
                    # Calculate balance after this leave
                    balance_after_leave = current_balance - leave.number_of_days
                    
                    # Check if negative cap is allowed
                    if not leave.holiday_status_id.allow_negative_cap and balance_after_leave < 0:
                        raise ValidationError(_(
                            'You cannot take more leave than your available balance. '
                            'Available balance: %.2f days, Requested: %.2f days'
                        ) % (current_balance, leave.number_of_days))
                    
                    # Check negative cap limit
                    elif leave.holiday_status_id.allow_negative_cap and balance_after_leave < -leave.holiday_status_id.negative_cap_limit:
                        raise ValidationError(_(
                            'You cannot exceed the negative cap limit. '
                            'Maximum negative balance allowed: %.2f days, '
                            'Balance after this leave would be: %.2f days'
                        ) % (leave.holiday_status_id.negative_cap_limit, balance_after_leave))

    @api.model
    def _get_leave_balance_info(self, employee_id, leave_type_id, date=None):
        """Get detailed leave balance information including negative cap"""
        if not date:
            date = fields.Date.today()
        
        leave_type = self.env['hr.leave.type'].browse(leave_type_id)
        leave_days = leave_type.get_employees_days([employee_id], date)
        
        if leave_type_id in leave_days and employee_id in leave_days[leave_type_id]:
            balance_info = leave_days[leave_type_id][employee_id]
            
            # Add negative cap information
            balance_info.update({
                'allow_negative_cap': leave_type.allow_negative_cap,
                'negative_cap_limit': leave_type.negative_cap_limit,
                'min_allowed_balance': -leave_type.negative_cap_limit if leave_type.allow_negative_cap else 0.0,
                'can_go_negative': leave_type.allow_negative_cap,
            })
            
            return balance_info
        
        return {
            'remaining_leaves': 0.0,
            'allow_negative_cap': leave_type.allow_negative_cap,
            'negative_cap_limit': leave_type.negative_cap_limit,
            'min_allowed_balance': -leave_type.negative_cap_limit if leave_type.allow_negative_cap else 0.0,
            'can_go_negative': leave_type.allow_negative_cap,
        }
