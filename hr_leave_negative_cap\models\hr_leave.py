# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class HrLeave(models.Model):
    _inherit = 'hr.leave'

    # Computed fields for negative cap information
    allow_negative_cap = fields.Boolean(
        related='holiday_status_id.allow_negative_cap',
        string='Allow Negative Cap',
        readonly=True
    )
    
    negative_cap_limit = fields.Float(
        related='holiday_status_id.negative_cap_limit',
        string='Negative Cap Limit',
        readonly=True
    )

    def _check_leave_type_validity(self):
        """Override the original method to include negative cap logic"""
        for leave in self:
            if leave.state in ('draft', 'confirm', 'validate1') and leave.holiday_status_id and leave.employee_id:
                if leave.holiday_status_id.requires_allocation == 'yes':
                    # Get current leave balance
                    leave_days = leave.holiday_status_id.get_employees_days([leave.employee_id.id])

                    if leave.holiday_status_id.id in leave_days and leave.employee_id.id in leave_days[leave.holiday_status_id.id]:
                        current_balance = leave_days[leave.holiday_status_id.id][leave.employee_id.id]['remaining_leaves']

                        # Calculate balance after this leave (excluding current leave if updating)
                        other_leaves = self.env['hr.leave'].search([
                            ('employee_id', '=', leave.employee_id.id),
                            ('holiday_status_id', '=', leave.holiday_status_id.id),
                            ('state', 'in', ['validate', 'validate1']),
                            ('id', '!=', leave.id)
                        ])
                        used_days = sum(other_leaves.mapped('number_of_days'))
                        available_balance = current_balance + used_days
                        balance_after_leave = available_balance - leave.number_of_days

                        # Check if negative cap is allowed
                        if not leave.holiday_status_id.allow_negative_cap and balance_after_leave < 0:
                            raise ValidationError(_(
                                'You cannot take more leave than your available balance. '
                                'Available balance: %.2f days, Requested: %.2f days'
                            ) % (available_balance, leave.number_of_days))

                        # Check negative cap limit
                        elif leave.holiday_status_id.allow_negative_cap and balance_after_leave < -leave.holiday_status_id.negative_cap_limit:
                            raise ValidationError(_(
                                'You cannot exceed the negative cap limit. '
                                'Maximum negative balance allowed: %.2f days, '
                                'Balance after this leave would be: %.2f days'
                            ) % (leave.holiday_status_id.negative_cap_limit, balance_after_leave))

        # Call the original method for other validations, but skip balance check
        # We handle balance check ourselves above
        pass

    def _check_holidays(self):
        """Override to handle negative cap validation"""
        for holiday in self:
            if holiday.holiday_type != 'employee' or not holiday.employee_id or holiday.holiday_status_id.allocation_type == 'no':
                continue

            # Skip validation if negative cap is allowed
            if holiday.holiday_status_id.allow_negative_cap:
                # Only check if we exceed the negative cap limit
                leave_days = holiday.holiday_status_id.get_employees_days([holiday.employee_id.id])
                if holiday.holiday_status_id.id in leave_days and holiday.employee_id.id in leave_days[holiday.holiday_status_id.id]:
                    remaining_leaves = leave_days[holiday.holiday_status_id.id][holiday.employee_id.id]['remaining_leaves']
                    if remaining_leaves - holiday.number_of_days < -holiday.holiday_status_id.negative_cap_limit:
                        raise ValidationError(_(
                            'You cannot exceed the negative cap limit of %.2f days. '
                            'Current balance: %.2f days, Requested: %.2f days'
                        ) % (holiday.holiday_status_id.negative_cap_limit, remaining_leaves, holiday.number_of_days))
            else:
                # Use original validation for non-negative cap leave types
                super(HrLeave, self)._check_holidays()

    @api.model
    def _get_leave_balance_info(self, employee_id, leave_type_id, date=None):
        """Get detailed leave balance information including negative cap"""
        if not date:
            date = fields.Date.today()
        
        leave_type = self.env['hr.leave.type'].browse(leave_type_id)
        leave_days = leave_type.get_employees_days([employee_id], date)
        
        if leave_type_id in leave_days and employee_id in leave_days[leave_type_id]:
            balance_info = leave_days[leave_type_id][employee_id]
            
            # Add negative cap information
            balance_info.update({
                'allow_negative_cap': leave_type.allow_negative_cap,
                'negative_cap_limit': leave_type.negative_cap_limit,
                'min_allowed_balance': -leave_type.negative_cap_limit if leave_type.allow_negative_cap else 0.0,
                'can_go_negative': leave_type.allow_negative_cap,
            })
            
            return balance_info
        
        return {
            'remaining_leaves': 0.0,
            'allow_negative_cap': leave_type.allow_negative_cap,
            'negative_cap_limit': leave_type.negative_cap_limit,
            'min_allowed_balance': -leave_type.negative_cap_limit if leave_type.allow_negative_cap else 0.0,
            'can_go_negative': leave_type.allow_negative_cap,
        }
