# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class HrLeaveType(models.Model):
    _inherit = 'hr.leave.type'

    # Negative Cap Fields
    allow_negative_cap = fields.Boolean(
        string='Allow Negative Cap',
        default=False,
        help='Allow employees to take more leave than their available balance'
    )
    
    negative_cap_limit = fields.Float(
        string='Negative Cap Limit',
        default=0.0,
        help='Maximum number of days that can be taken beyond the available balance'
    )

    @api.constrains('negative_cap_limit')
    def _check_negative_cap_limit(self):
        """Validate negative cap limit"""
        for record in self:
            if record.allow_negative_cap and record.negative_cap_limit < 0:
                raise ValidationError(_('Negative cap limit must be a positive number or zero.'))

    @api.onchange('allow_negative_cap')
    def _onchange_allow_negative_cap(self):
        """Reset negative cap limit when allow negative cap is disabled"""
        if not self.allow_negative_cap:
            self.negative_cap_limit = 0.0

    def get_employees_days(self, employee_ids, date=None):
        """Override to include negative cap information and show negative balances"""
        result = super(HrLeaveType, self).get_employees_days(employee_ids, date)

        # Add negative cap information and recalculate balances
        for leave_type in self:
            if leave_type.id in result:
                for employee_id in employee_ids:
                    if employee_id in result[leave_type.id]:
                        # Add negative cap info
                        result[leave_type.id][employee_id]['allow_negative_cap'] = leave_type.allow_negative_cap
                        result[leave_type.id][employee_id]['negative_cap_limit'] = leave_type.negative_cap_limit

                        # Recalculate balance to show negative values correctly
                        if leave_type.requires_allocation == 'yes':
                            # Get actual allocations
                            allocations = self.env['hr.leave.allocation'].search([
                                ('employee_id', '=', employee_id),
                                ('holiday_status_id', '=', leave_type.id),
                                ('state', 'in', ['validate', 'validate1']),
                            ])
                            total_allocated = sum(allocations.mapped('number_of_days'))

                            # Get actual leaves taken
                            leaves = self.env['hr.leave'].search([
                                ('employee_id', '=', employee_id),
                                ('holiday_status_id', '=', leave_type.id),
                                ('state', 'in', ['validate', 'validate1']),
                            ])
                            total_taken = sum(leaves.mapped('number_of_days'))

                            # Calculate real remaining balance (can be negative)
                            real_balance = total_allocated - total_taken

                            # Update the result to show real balance
                            result[leave_type.id][employee_id]['remaining_leaves'] = real_balance
                            result[leave_type.id][employee_id]['virtual_remaining_leaves'] = real_balance

                        # Set minimum balance based on negative cap
                        if leave_type.allow_negative_cap:
                            result[leave_type.id][employee_id]['min_balance'] = -leave_type.negative_cap_limit
                        else:
                            result[leave_type.id][employee_id]['min_balance'] = 0.0

        return result

    def get_allocation_data_request(self):
        """Override to show negative balances in UI"""
        result = super(HrLeaveType, self).get_allocation_data_request()

        # Modify result to show negative balances when negative cap is enabled
        for leave_type_data in result:
            leave_type = self.browse(leave_type_data['id'])
            if leave_type.allow_negative_cap:
                # Recalculate to show actual balance including negatives
                employee_id = self.env.user.employee_id.id
                if employee_id:
                    leave_days = leave_type.get_employees_days([employee_id])
                    if leave_type.id in leave_days and employee_id in leave_days[leave_type.id]:
                        actual_balance = leave_days[leave_type.id][employee_id]['remaining_leaves']
                        leave_type_data['remaining_leaves'] = actual_balance
                        leave_type_data['virtual_remaining_leaves'] = actual_balance

                        # Add negative cap info to display
                        leave_type_data['allow_negative_cap'] = True
                        leave_type_data['negative_cap_limit'] = leave_type.negative_cap_limit

        return result

    def _get_employees_days_request(self, employee_ids, date):
        """Override to handle negative cap in leave requests"""
        result = super(HrLeaveType, self)._get_employees_days_request(employee_ids, date)

        # Allow negative balance if negative cap is enabled
        for leave_type in self:
            if leave_type.allow_negative_cap and leave_type.id in result:
                for employee_id in employee_ids:
                    if employee_id in result[leave_type.id]:
                        # Modify the validation to allow negative balance up to the limit
                        current_balance = result[leave_type.id][employee_id].get('remaining_leaves', 0)
                        if current_balance < 0 and abs(current_balance) <= leave_type.negative_cap_limit:
                            # Allow this negative balance
                            result[leave_type.id][employee_id]['virtual_remaining_leaves'] = current_balance

        return result
