# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class HrLeaveType(models.Model):
    _inherit = 'hr.leave.type'

    # Negative Cap Fields
    allow_negative_cap = fields.Boolean(
        string='Allow Negative Cap',
        default=False,
        help='Allow employees to take more leave than their available balance'
    )
    
    negative_cap_limit = fields.Float(
        string='Negative Cap Limit',
        default=0.0,
        help='Maximum number of days that can be taken beyond the available balance'
    )

    @api.constrains('negative_cap_limit')
    def _check_negative_cap_limit(self):
        """Validate negative cap limit"""
        for record in self:
            if record.allow_negative_cap and record.negative_cap_limit < 0:
                raise ValidationError(_('Negative cap limit must be a positive number or zero.'))

    @api.onchange('allow_negative_cap')
    def _onchange_allow_negative_cap(self):
        """Reset negative cap limit when allow negative cap is disabled"""
        if not self.allow_negative_cap:
            self.negative_cap_limit = 0.0

    def get_employees_days(self, employee_ids, date=None):
        """Override to include negative cap information"""
        result = super(HrLeaveType, self).get_employees_days(employee_ids, date)
        
        # Add negative cap information to the result
        for leave_type in self:
            if leave_type.id in result:
                for employee_id in employee_ids:
                    if employee_id in result[leave_type.id]:
                        result[leave_type.id][employee_id]['allow_negative_cap'] = leave_type.allow_negative_cap
                        result[leave_type.id][employee_id]['negative_cap_limit'] = leave_type.negative_cap_limit
                        
                        # Calculate effective minimum balance
                        if leave_type.allow_negative_cap:
                            result[leave_type.id][employee_id]['min_balance'] = -leave_type.negative_cap_limit
                        else:
                            result[leave_type.id][employee_id]['min_balance'] = 0.0
        
        return result
