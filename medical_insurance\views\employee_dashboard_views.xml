<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Employee Dashboard View -->
    <record id="view_employee_medical_dashboard" model="ir.ui.view">
        <field name="name">employee.medical.dashboard</field>
        <field name="model">employee.dashboard</field>
        <field name="arch" type="xml">
            <form string="My Medical Insurance Dashboard" class="o_medical_dashboard" create="false" edit="false" delete="false" duplicate="false" js_class="medical_dashboard_form">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <span>لوحة معلومات التأمين الطبي</span>
                        </h1>
                    </div>

                    <!-- Employee Profile Section -->
                    <div class="row mt16 mb16">
                        <div class="col-md-3 text-center">
                            <div class="card">
                                <div class="card-body">
                                    <field name="employee_image" widget="image" class="oe_avatar rounded-circle" options="{'size': [150, 150]}"/>
                                    <h3 class="mt8"><field name="employee_name" readonly="1"/></h3>
                                    <p><field name="employee_job" readonly="1"/></p>
                                    <p><field name="employee_department" readonly="1"/></p>
                                    <p><field name="employee_number" readonly="1"/></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card h-100">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="card-title mb-0">المعلومات الشخصية</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-5 font-weight-bold">العمر:</div>
                                        <div class="col-7"><field name="employee_age" readonly="1"/> سنة</div>
                                    </div>
                                    <div class="row mt-2" attrs="{'invisible': [('employee_blood_type', '=', False)]}">
                                        <div class="col-5 font-weight-bold">فصيلة الدم:</div>
                                        <div class="col-7"><field name="employee_blood_type" readonly="1"/></div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-5 font-weight-bold">الحالة الاجتماعية:</div>
                                        <div class="col-7"><field name="employee_marital" readonly="1"/></div>
                                    </div>
                                    <div class="row mt-2" attrs="{'invisible': [('employee_mobile', '=', False)]}">
                                        <div class="col-5 font-weight-bold">الهاتف:</div>
                                        <div class="col-7"><field name="employee_mobile" readonly="1"/></div>
                                    </div>
                                    <div class="row mt-2" attrs="{'invisible': [('employee_work_email', '=', False)]}">
                                        <div class="col-5 font-weight-bold">البريد الإلكتروني:</div>
                                        <div class="col-7"><field name="employee_work_email" readonly="1"/></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-success text-white">
                                    <h5 class="card-title mb-0">معلومات التأمين الطبي</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="text-center mb-3">
                                                <h3 class="text-primary"><field name="employee_cap" widget="monetary" options="{'currency_field': 'company_currency_id'}" readonly="1"/></h3>
                                                <p>الحد السنوي</p>
                                            </div>
                                            <div class="text-center">
                                                <h3 class="text-success"><field name="remaining_balance" widget="monetary" options="{'currency_field': 'company_currency_id'}" readonly="1"/></h3>
                                                <p>الرصيد المتبقي</p>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="text-center mb-3">
                                                <h3 class="text-info"><field name="previous_claims_amount" widget="monetary" options="{'currency_field': 'company_currency_id'}" readonly="1"/></h3>
                                                <p>المطالبات السابقة</p>
                                            </div>
                                            <div class="text-center">
                                                <h3 class="text-warning"><field name="reimbursement_rate" widget="percentage" readonly="1"/></h3>
                                                <p>نسبة التعويض</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Family Members Section -->
                    <div class="row mt16 mb16" attrs="{'invisible': [('family_member_count', '=', 0)]}">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h5 class="card-title mb-0">أفراد الأسرة (<field name="family_member_count" readonly="1"/>)</h5>
                                </div>
                                <div class="card-body p-0">
                                    <field name="family_member_ids" readonly="1" widget="one2many" mode="tree" nolabel="1" options="{'no_open': True}">
                                        <tree create="false" delete="false" edit="false">
                                            <field name="name"/>
                                            <field name="relationship"/>
                                            <field name="birth_date" optional="show"/>
                                        </tree>
                                    </field>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Claims Statistics -->
                    <div class="row mt16 mb16">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h2 class="card-title"><field name="total_claims_count" readonly="1"/></h2>
                                    <p class="card-text">إجمالي المطالبات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h2 class="card-title"><field name="approved_claims_count" readonly="1"/></h2>
                                    <p class="card-text">المطالبات الموافق عليها</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h2 class="card-title"><field name="pending_claims_count" readonly="1"/></h2>
                                    <p class="card-text">المطالبات قيد الانتظار</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h2 class="card-title"><field name="rejected_claims_count" readonly="1"/></h2>
                                    <p class="card-text">المطالبات المرفوضة</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Charts and Graphs -->
                    <div class="row mt16 mb16">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="card-title mb-0">المطالبات حسب الحالة</h5>
                                </div>
                                <div class="card-body">
                                    <pre><field name="claims_by_state_graph" readonly="1"/></pre>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h5 class="card-title mb-0">المطالبات حسب النوع</h5>
                                </div>
                                <div class="card-body">
                                    <pre><field name="claims_by_type_graph" readonly="1"/></pre>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Monthly Claims Chart -->
                    <div class="row mt16 mb16">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-warning text-white">
                                    <h5 class="card-title mb-0">المطالبات الشهرية</h5>
                                </div>
                                <div class="card-body">
                                    <pre><field name="monthly_claims_graph" readonly="1"/></pre>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Facilities Section -->
                    <div class="row mt16 mb16" attrs="{'invisible': [('top_facilities_ids', '=', [])]}">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="card-title mb-0">المراكز الطبية الأكثر استخداماً</h5>
                                </div>
                                <div class="card-body p-0">
                                    <field name="top_facilities_ids" readonly="1" widget="many2many" mode="tree" nolabel="1" options="{'no_open': True}">
                                        <tree create="false" delete="false" edit="false">
                                            <field name="name"/>
                                            <field name="facility_type"/>
                                            <field name="code" optional="show"/>
                                        </tree>
                                    </field>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Claims -->
                    <div class="row mt16 mb16">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h5 class="card-title mb-0">آخر المطالبات</h5>
                                </div>
                                <div class="card-body p-0">
                                    <field name="recent_claims_ids" readonly="1" widget="many2many" mode="tree" nolabel="1" options="{'no_open': False}">
                                        <tree create="false" delete="false" edit="false" decoration-info="state == 'draft'" decoration-primary="state == 'submitted'" decoration-warning="state == 'approved'" decoration-danger="state == 'rejected'" decoration-success="state == 'paid'">
                                            <field name="name"/>
                                            <field name="date"/>
                                            <field name="beneficiary_type"/>
                                            <field name="family_member_id" optional="show"/>
                                            <field name="claim_type"/>
                                            <field name="facility_id"/>
                                            <field name="department_id" optional="show"/>
                                            <field name="total_amount" sum="المجموع"/>
                                            <field name="reimbursed_amount" sum="المجموع المسترد"/>
                                            <field name="state" widget="badge"/>
                                        </tree>
                                    </field>
                                </div>
                            </div>
                        </div>
                    </div>


                </sheet>
            </form>
        </field>
    </record>

    <!-- Employee Dashboard Action -->
    <record id="action_employee_medical_dashboard" model="ir.actions.act_window">
        <field name="name">My Medical Dashboard</field>
        <field name="res_model">employee.dashboard</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_employee_medical_dashboard"/>
        <field name="target">current</field>
        <field name="context">{'create': False, 'form_view_initial_mode': 'readonly', 'no_breadcrumbs': True, 'form_view_ref': 'readonly'}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No medical claims found
            </p>
            <p>
                Here you can view your medical insurance dashboard.
            </p>
        </field>
    </record>

    <!-- Hide Edit Button in Form View -->
    <record id="edit_form_view_action_employee_dashboard" model="ir.actions.act_window.view">
        <field name="sequence" eval="1"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_employee_medical_dashboard"/>
        <field name="act_window_id" ref="action_employee_medical_dashboard"/>
    </record>

    <!-- Server Action to Load Dashboard -->
    <record id="action_load_dashboard" model="ir.actions.server">
        <field name="name">Load Dashboard</field>
        <field name="model_id" ref="model_employee_dashboard"/>
        <field name="state">code</field>
        <field name="code">
dashboard_id = model.get_dashboard_data()
if dashboard_id:
    # Return an action to open the dashboard
    action = {
        'type': 'ir.actions.act_window',
        'res_model': 'employee.dashboard',
        'view_mode': 'form',
        'res_id': dashboard_id,
        'view_id': model.env.ref('medical_insurance.view_employee_medical_dashboard').id,
        'target': 'current',
        'context': {'create': False, 'form_view_initial_mode': 'readonly', 'no_breadcrumbs': True, 'form_view_ref': 'readonly'},
    }
else:
    # If no dashboard found, show a warning
    action = {
        'type': 'ir.actions.client',
        'tag': 'display_notification',
        'params': {
            'title': 'Warning',
            'message': 'No employee found for current user',
            'sticky': False,
            'type': 'warning',
        }
    }
result = action
        </field>
    </record>
</odoo>
