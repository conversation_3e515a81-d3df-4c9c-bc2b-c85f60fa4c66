# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* medical_insurance
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-06-22 12:00+0000\n"
"PO-Revision-Date: 2024-06-22 12:00+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: medical_insurance
#: model:ir.model.fields.selection,name:medical_insurance.selection__employee_dashboard__employee_marital__single
msgid "Single"
msgstr "أعزب"

#. module: medical_insurance
#: model:ir.model.fields.selection,name:medical_insurance.selection__employee_dashboard__employee_marital__married
msgid "Married"
msgstr "متزوج"

#. module: medical_insurance
#: model:ir.model.fields.selection,name:medical_insurance.selection__employee_dashboard__employee_marital__married_and_provide
msgid "Married with Dependents"
msgstr "متزوج ويعول"

#. module: medical_insurance
#: model:ir.model.fields.selection,name:medical_insurance.selection__employee_dashboard__employee_marital__cohabitant
msgid "Legal Cohabitant"
msgstr "شريك قانوني"

#. module: medical_insurance
#: model:ir.model.fields.selection,name:medical_insurance.selection__employee_dashboard__employee_marital__widower
msgid "Widower"
msgstr "أرمل"

#. module: medical_insurance
#: model:ir.model.fields.selection,name:medical_insurance.selection__employee_dashboard__employee_marital__divorced
msgid "Divorced"
msgstr "مطلق"

#. module: medical_insurance
#: model:ir.model.fields,field_description:medical_insurance.field_employee_dashboard__employee_marital
msgid "Marital Status"
msgstr "الحالة الاجتماعية"
