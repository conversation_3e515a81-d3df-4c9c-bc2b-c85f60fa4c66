#!/bin/bash

# Quick Install Script for HR Leave Negative Cap Module
# =====================================================

echo "🚀 HR Leave Negative Cap Module - Quick Install"
echo "=============================================="

# Check if we're in the right directory
if [ ! -f "__manifest__.py" ]; then
    echo "❌ Error: Please run this script from the hr_leave_negative_cap directory"
    exit 1
fi

echo "✅ Module directory found"

# Check manifest file
if [ -f "__manifest__.py" ]; then
    echo "✅ Manifest file exists"
else
    echo "❌ Error: __manifest__.py not found"
    exit 1
fi

# Check required files
required_files=("models/__init__.py" "models/hr_leave_type.py" "models/hr_leave.py" "views/hr_leave_type_views.xml")

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file exists"
    else
        echo "❌ Error: $file not found"
        exit 1
    fi
done

echo ""
echo "📋 Installation Instructions:"
echo "=============================="
echo ""
echo "1. Copy this module to your Odoo addons directory:"
echo "   cp -r hr_leave_negative_cap /path/to/odoo/addons/"
echo ""
echo "2. Restart Odoo server:"
echo "   sudo systemctl restart odoo"
echo "   # OR"
echo "   ./odoo-bin --addons-path=addons --update=all"
echo ""
echo "3. Install the module:"
echo "   - Go to Apps in Odoo"
echo "   - Remove 'Apps' filter"
echo "   - Search for 'hr_leave_negative_cap'"
echo "   - Click Install"
echo ""
echo "4. Configure leave types:"
echo "   - Go to Time Off → Configuration → Leave Types"
echo "   - Edit a leave type"
echo "   - Configure NEGATIVE CAP section"
echo ""
echo "🎯 Module Features:"
echo "=================="
echo "✅ Allow Negative Cap checkbox"
echo "✅ Configurable negative cap limit"
echo "✅ Automatic validation"
echo "✅ Arabic translations"
echo "✅ Clean UI design"
echo ""
echo "📞 Support: Contact your development team for assistance"
echo ""
echo "🎉 Module is ready for installation!"
