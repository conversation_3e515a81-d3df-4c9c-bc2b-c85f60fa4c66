<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Demo Leave Type with Negative Cap -->
        <record id="demo_leave_type_annual_negative" model="hr.leave.type">
            <field name="name">Annual Leave (Negative Cap Demo)</field>
            <field name="allocation_type">fixed</field>
            <field name="validity_start" eval="(DateTime.today() - timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="validity_stop" eval="(DateTime.today() + timedelta(days=365)).strftime('%Y-%m-%d')"/>
            <field name="allow_negative_cap">True</field>
            <field name="negative_cap_limit">5.0</field>
            <field name="request_unit">day</field>
            <field name="color_name">red</field>
        </record>

        <!-- Demo Leave Type with Higher Negative Cap -->
        <record id="demo_leave_type_sick_negative" model="hr.leave.type">
            <field name="name">Sick Leave (Negative Cap Demo)</field>
            <field name="allocation_type">fixed</field>
            <field name="validity_start" eval="(DateTime.today() - timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="validity_stop" eval="(DateTime.today() + timedelta(days=365)).strftime('%Y-%m-%d')"/>
            <field name="allow_negative_cap">True</field>
            <field name="negative_cap_limit">3.0</field>
            <field name="request_unit">day</field>
            <field name="color_name">blue</field>
        </record>

        <!-- Demo Leave Type without Negative Cap -->
        <record id="demo_leave_type_personal_no_negative" model="hr.leave.type">
            <field name="name">Personal Leave (No Negative Cap Demo)</field>
            <field name="allocation_type">fixed</field>
            <field name="validity_start" eval="(DateTime.today() - timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="validity_stop" eval="(DateTime.today() + timedelta(days=365)).strftime('%Y-%m-%d')"/>
            <field name="allow_negative_cap">False</field>
            <field name="negative_cap_limit">0.0</field>
            <field name="request_unit">day</field>
            <field name="color_name">green</field>
        </record>

    </data>
</odoo>
