# إصلاحات موديول HR Leave Negative Cap

## ✅ الإصلاحات المطبقة

### 1. تعديل مكان عرض NEGATIVE CAP
**المشكلة**: كان القسم يظهر في مكان يتداخل مع الحقول الأخرى
**الحل**: تم نقل القسم إلى اليمين تحت مجموعة "Approval"

```xml
<!-- قبل الإصلاح -->
<field name="request_unit" position="after">

<!-- بعد الإصلاح -->
<xpath expr="//group[@name='approval']" position="after">
```

### 2. إصلاح تطبيق القاعدة
**المشكلة**: كان يمكن تجاوز عدد الأيام المسموح بأخذها بالسالب
**الحل**: تم إضافة `@api.constrains` للتحقق الصارم

```python
@api.constrains('state', 'number_of_days', 'holiday_status_id', 'employee_id')
def _check_negative_cap_constraint(self):
    # حساب الرصيد النهائي
    final_balance = total_allocated - total_used
    
    # التحقق من الحدود
    if (leave.holiday_status_id.allow_negative_cap and 
        final_balance < -leave.holiday_status_id.negative_cap_limit):
        raise ValidationError(...)
```

### 3. عرض الرصيد السالب
**المشكلة**: لم يكن الرصيد السالب يظهر بشكل صحيح
**الحل**: تم تعديل `get_employees_days` لإظهار الرصيد الحقيقي

```python
def get_employees_days(self, employee_ids, date=None):
    # حساب الرصيد الحقيقي (يمكن أن يكون سالب)
    real_balance = total_allocated - total_taken
    result[leave_type.id][employee_id]['remaining_leaves'] = real_balance
```

### 4. تحسين واجهة المستخدم
**التحسينات**:
- ✅ Checkbox بدلاً من toggle
- ✅ تخطيط أفضل مع `col="2"`
- ✅ عرض مناسب للحقل الرقمي (80px)
- ✅ موضع أفضل تحت مجموعة Approval

## 🎯 النتائج المتوقعة

### عند تفعيل Negative Cap:
1. **الإعداد**:
   - ☑️ Allow Negative Cap ?
   - up to [5] days

2. **السيناريو**:
   - الموظف لديه: 2 أيام
   - يطلب: 7 أيام
   - النتيجة: ✅ مسموح (الرصيد = -5)

3. **عرض الرصيد**:
   - قبل الطلب: +2 أيام
   - بعد الطلب: -5 أيام (يظهر بالسالب)

4. **الحد الأقصى**:
   - يطلب: 10 أيام
   - النتيجة: ❌ مرفوض (الرصيد سيكون -8، يتجاوز الحد -5)

## 🔧 الملفات المعدلة

1. **views/hr_leave_type_views.xml**:
   - نقل موضع NEGATIVE CAP
   - تحسين التخطيط

2. **models/hr_leave.py**:
   - إضافة `@api.constrains` للتحقق الصارم
   - تحسين عرض الرصيد

3. **models/hr_leave_type.py**:
   - تعديل `get_employees_days` لإظهار الرصيد السالب
   - إضافة `get_allocation_data_request`

## 🚀 خطوات الاختبار

1. **تثبيت الموديول**
2. **إعداد نوع إجازة**:
   - اذهب إلى Time Off → Configuration → Leave Types
   - اختر نوع إجازة
   - فعل "Allow Negative Cap ?" 
   - ضع حد (مثل 5 أيام)

3. **اختبار الوظيفة**:
   - أنشئ موظف برصيد محدود (مثل 2 أيام)
   - اطلب إجازة 7 أيام (ضمن الحد)
   - تحقق من قبول الطلب
   - تحقق من عرض الرصيد السالب (-5)

4. **اختبار الحد الأقصى**:
   - اطلب إجازة 10 أيام (يتجاوز الحد)
   - تحقق من رفض الطلب مع رسالة خطأ واضحة

## ✅ التأكيدات

- ✅ لا يتداخل مع الحقول الأخرى
- ✅ القاعدة تطبق بشكل صحيح
- ✅ الرصيد السالب يظهر بوضوح
- ✅ لا يؤثر على العروض الأصلية
- ✅ رسائل خطأ واضحة بالعربية والإنجليزية
