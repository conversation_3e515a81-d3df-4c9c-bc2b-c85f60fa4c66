# -*- coding: utf-8 -*-
{
    'name': 'HR Leave Negative Cap',
    'version': '********.0',
    'category': 'Human Resources/Time Off',
    'summary': 'Add negative cap functionality to leave types',
    'description': """
This module extends the HR Leave functionality by adding negative cap capabilities.

Features:
* Allow Negative Cap option for leave types
* Configurable negative cap limit
* Enhanced leave balance validation
* Add "Allow Negative Cap" checkbox to leave types
* Set maximum negative days allowed
* Automatic validation when requesting leaves
* Arabic translation support

This allows employees to take more leave than their available balance up to a configurable limit.
    """,
    'author': 'Your Company',
    'website': 'https://www.yourcompany.com',
    'depends': ['hr_holidays'],
    'data': [
        'security/ir.model.access.csv',
        'views/hr_leave_type_views.xml',
    ],
    'demo': [
        'demo/hr_leave_type_demo.xml',
    ],
    'installable': True,
    'application': False,
    'auto_install': False,
    'license': 'LGPL-3',
}
