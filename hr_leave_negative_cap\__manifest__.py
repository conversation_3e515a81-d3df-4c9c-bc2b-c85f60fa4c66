# -*- coding: utf-8 -*-
{
    'name': 'HR Leave Negative Cap',
    'version': '********.0',
    'category': 'Human Resources/Time Off',
    'summary': 'Add negative cap functionality to leave types',
    'description': """
        HR Leave Negative Cap
        =====================
        
        This module extends the HR Leave functionality by adding:
        - Allow Negative Cap option for leave types
        - Configurable negative cap limit
        - Enhanced leave balance validation
        
        Features:
        - Add "Allow Negative Cap" checkbox to leave types
        - Set maximum negative days allowed
        - Automatic validation when requesting leaves
        - Arabic translation support
    """,
    'author': 'Your Company',
    'website': 'https://www.yourcompany.com',
    'depends': ['hr_holidays'],
    'data': [
        'security/ir.model.access.csv',
        'views/hr_leave_type_views.xml',
    ],
    'demo': [
        'demo/hr_leave_type_demo.xml',
    ],
    'installable': True,
    'application': False,
    'auto_install': False,
    'license': 'LGPL-3',
}
