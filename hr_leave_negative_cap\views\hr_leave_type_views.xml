<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Inherit Leave Type Form View -->
    <record id="view_hr_leave_type_form_inherit_negative_cap" model="ir.ui.view">
        <field name="name">hr.leave.type.form.inherit.negative.cap</field>
        <field name="model">hr.leave.type</field>
        <field name="inherit_id" ref="hr_holidays.edit_holiday_status_form"/>
        <field name="arch" type="xml">
            <!-- Add Negative Cap section after Approval group -->
            <xpath expr="//group[@name='approval']" position="after">
                <group string="NEGATIVE CAP" name="negative_cap_group" col="2">
                    <field name="allow_negative_cap" string="Allow Negative Cap ?"/>
                    <div class="o_row" attrs="{'invisible': [('allow_negative_cap', '=', False)]}">
                        <span>up to</span>
                        <field name="negative_cap_limit"
                               attrs="{'required': [('allow_negative_cap', '=', True)],
                                      'invisible': [('allow_negative_cap', '=', False)]}"
                               class="oe_inline" style="width: 80px;"/>
                        <span>days</span>
                    </div>
                </group>
            </xpath>
        </field>
    </record>


</odoo>
