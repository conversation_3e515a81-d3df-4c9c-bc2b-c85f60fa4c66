<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Inherit Leave Type Form View -->
    <record id="view_hr_leave_type_form_inherit_negative_cap" model="ir.ui.view">
        <field name="name">hr.leave.type.form.inherit.negative.cap</field>
        <field name="model">hr.leave.type</field>
        <field name="inherit_id" ref="hr_holidays.edit_holiday_status_form"/>
        <field name="arch" type="xml">
            <!-- Add Negative Cap section -->
            <sheet position="inside">
                <group string="NEGATIVE CAP" name="negative_cap_group">
                    <div class="o_row">
                        <field name="allow_negative_cap" widget="boolean_toggle"/>
                        <label for="allow_negative_cap" string="Allow Negative Cap ?"/>
                    </div>
                    <div class="o_row" attrs="{'invisible': [('allow_negative_cap', '=', False)]}">
                        <span>up to</span>
                        <field name="negative_cap_limit"
                               attrs="{'required': [('allow_negative_cap', '=', True)],
                                      'invisible': [('allow_negative_cap', '=', False)]}"
                               class="oe_inline"/>
                        <span>days</span>
                    </div>
                </group>
            </sheet>
        </field>
    </record>

    <!-- Simple Leave Type Tree View -->
    <record id="view_hr_leave_type_tree_inherit_negative_cap" model="ir.ui.view">
        <field name="name">hr.leave.type.tree.inherit.negative.cap</field>
        <field name="model">hr.leave.type</field>
        <field name="inherit_id" ref="hr_holidays.view_holiday_status_normal_tree"/>
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="allow_negative_cap" optional="hide"/>
                <field name="negative_cap_limit" optional="hide"/>
            </field>
        </field>
    </record>
</odoo>
