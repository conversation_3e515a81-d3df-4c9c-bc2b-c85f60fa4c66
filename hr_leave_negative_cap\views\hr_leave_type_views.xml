<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Inherit Leave Type Form View -->
    <record id="view_hr_leave_type_form_inherit_negative_cap" model="ir.ui.view">
        <field name="name">hr.leave.type.form.inherit.negative.cap</field>
        <field name="model">hr.leave.type</field>
        <field name="inherit_id" ref="hr_holidays.edit_holiday_status_form"/>
        <field name="arch" type="xml">
            <!-- Add Negative Cap section after the existing fields -->
            <xpath expr="//group[@name='validity']" position="after">
                <group string="NEGATIVE CAP" name="negative_cap_group">
                    <div class="o_row">
                        <field name="allow_negative_cap" widget="boolean_toggle"/>
                        <label for="allow_negative_cap" string="Allow Negative Cap ?"/>
                    </div>
                    <div class="o_row" attrs="{'invisible': [('allow_negative_cap', '=', False)]}">
                        <span>up to</span>
                        <field name="negative_cap_limit" 
                               attrs="{'required': [('allow_negative_cap', '=', True)],
                                      'invisible': [('allow_negative_cap', '=', False)]}"
                               class="oe_inline"/>
                        <span>days</span>
                    </div>
                </group>
            </xpath>
        </field>
    </record>

    <!-- Inherit Leave Type Tree View to show negative cap info -->
    <record id="view_hr_leave_type_tree_inherit_negative_cap" model="ir.ui.view">
        <field name="name">hr.leave.type.tree.inherit.negative.cap</field>
        <field name="model">hr.leave.type</field>
        <field name="inherit_id" ref="hr_holidays.view_holiday_status_normal_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='allocation_type']" position="after">
                <field name="allow_negative_cap" optional="hide"/>
                <field name="negative_cap_limit" optional="hide" 
                       attrs="{'invisible': [('allow_negative_cap', '=', False)]}"/>
            </xpath>
        </field>
    </record>

    <!-- Inherit Leave Request Form View to show negative cap information -->
    <record id="view_hr_leave_form_inherit_negative_cap" model="ir.ui.view">
        <field name="name">hr.leave.form.inherit.negative.cap</field>
        <field name="model">hr.leave</field>
        <field name="inherit_id" ref="hr_holidays.edit_holiday_new"/>
        <field name="arch" type="xml">
            <!-- Add negative cap information in the form -->
            <xpath expr="//field[@name='number_of_days_display']" position="after">
                <div attrs="{'invisible': [('allow_negative_cap', '=', False)]}" 
                     class="alert alert-info" role="alert">
                    <i class="fa fa-info-circle"/> 
                    <strong>Negative Cap Allowed:</strong> 
                    You can take up to <field name="negative_cap_limit" readonly="1"/> days 
                    beyond your available balance.
                </div>
            </xpath>
            
            <!-- Add invisible fields for computation -->
            <xpath expr="//field[@name='holiday_status_id']" position="after">
                <field name="allow_negative_cap" invisible="1"/>
                <field name="negative_cap_limit" invisible="1"/>
            </xpath>
        </field>
    </record>
</odoo>
