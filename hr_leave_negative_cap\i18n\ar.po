# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_leave_negative_cap
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-06-22 12:00+0000\n"
"PO-Revision-Date: 2024-06-22 12:00+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: hr_leave_negative_cap
#: model:ir.model.fields,field_description:hr_leave_negative_cap.field_hr_leave_type__allow_negative_cap
#: model:ir.model.fields,field_description:hr_leave_negative_cap.field_hr_leave__allow_negative_cap
msgid "Allow Negative Cap"
msgstr "السماح بالرصيد السالب"

#. module: hr_leave_negative_cap
#: model:ir.model.fields,help:hr_leave_negative_cap.field_hr_leave_type__allow_negative_cap
msgid "Allow employees to take more leave than their available balance"
msgstr "السماح للموظفين بأخذ إجازة أكثر من رصيدهم المتاح"

#. module: hr_leave_negative_cap
#: model:ir.model.fields,field_description:hr_leave_negative_cap.field_hr_leave_type__negative_cap_limit
#: model:ir.model.fields,field_description:hr_leave_negative_cap.field_hr_leave__negative_cap_limit
msgid "Negative Cap Limit"
msgstr "حد الرصيد السالب"

#. module: hr_leave_negative_cap
#: model:ir.model.fields,help:hr_leave_negative_cap.field_hr_leave_type__negative_cap_limit
msgid "Maximum number of days that can be taken beyond the available balance"
msgstr "الحد الأقصى لعدد الأيام التي يمكن أخذها بعد الرصيد المتاح"

#. module: hr_leave_negative_cap
#: model:ir.ui.view,arch_db:hr_leave_negative_cap.view_hr_leave_type_form_inherit_negative_cap
msgid "NEGATIVE CAP"
msgstr "الرصيد السالب"

#. module: hr_leave_negative_cap
#: model:ir.ui.view,arch_db:hr_leave_negative_cap.view_hr_leave_type_form_inherit_negative_cap
msgid "Allow Negative Cap ?"
msgstr "السماح بالرصيد السالب؟"

#. module: hr_leave_negative_cap
#: model:ir.ui.view,arch_db:hr_leave_negative_cap.view_hr_leave_type_form_inherit_negative_cap
msgid "up to"
msgstr "حتى"

#. module: hr_leave_negative_cap
#: model:ir.ui.view,arch_db:hr_leave_negative_cap.view_hr_leave_type_form_inherit_negative_cap
msgid "days"
msgstr "أيام"

#. module: hr_leave_negative_cap
#: model:ir.ui.view,arch_db:hr_leave_negative_cap.view_hr_leave_form_inherit_negative_cap
msgid "Negative Cap Allowed:"
msgstr "الرصيد السالب مسموح:"

#. module: hr_leave_negative_cap
#: model:ir.ui.view,arch_db:hr_leave_negative_cap.view_hr_leave_form_inherit_negative_cap
msgid "You can take up to"
msgstr "يمكنك أخذ حتى"

#. module: hr_leave_negative_cap
#: model:ir.ui.view,arch_db:hr_leave_negative_cap.view_hr_leave_form_inherit_negative_cap
msgid "days beyond your available balance."
msgstr "أيام بعد رصيدك المتاح."

#. module: hr_leave_negative_cap
#: code:addons/hr_leave_negative_cap/models/hr_leave_type.py:0
msgid "Negative cap limit must be a positive number or zero."
msgstr "حد الرصيد السالب يجب أن يكون رقم موجب أو صفر."

#. module: hr_leave_negative_cap
#: code:addons/hr_leave_negative_cap/models/hr_leave.py:0
msgid "You cannot take more leave than your available balance. Available balance: %.2f days, Requested: %.2f days"
msgstr "لا يمكنك أخذ إجازة أكثر من رصيدك المتاح. الرصيد المتاح: %.2f أيام، المطلوب: %.2f أيام"

#. module: hr_leave_negative_cap
#: code:addons/hr_leave_negative_cap/models/hr_leave.py:0
msgid "You cannot exceed the negative cap limit. Maximum negative balance allowed: %.2f days, Balance after this leave would be: %.2f days"
msgstr "لا يمكنك تجاوز حد الرصيد السالب. الحد الأقصى للرصيد السالب المسموح: %.2f أيام، الرصيد بعد هذه الإجازة سيكون: %.2f أيام"
