# HR Leave Negative Cap

## Overview

This module extends the HR Leave functionality in Odoo by adding negative cap capabilities to leave types. It allows employees to take more leave than their available balance up to a configurable limit.

## Features

### 1. Negative Cap Configuration
- **Allow Negative Cap**: Checkbox to enable/disable negative cap for each leave type
- **Negative Cap Limit**: Configurable maximum number of days that can be taken beyond available balance

### 2. Enhanced Leave Validation
- Automatic validation when employees request leaves
- Prevents exceeding the configured negative cap limit
- Clear error messages in both English and Arabic

### 3. User Interface Enhancements
- Clean UI matching the provided design
- Information alerts showing negative cap allowance
- Tree view columns for negative cap information

### 4. Arabic Translation Support
- Complete Arabic translation for all new fields and messages
- RTL-friendly interface

## Installation

1. Copy the `hr_leave_negative_cap` folder to your Odoo addons directory
2. Update the addons list in Odoo
3. Install the module from Apps menu

## Usage

### For HR Managers

1. **Configure Leave Types**:
   - Go to Time Off → Configuration → Leave Types
   - Edit or create a leave type
   - In the "NEGATIVE CAP" section:
     - Check "Allow Negative Cap ?" to enable
     - Set the maximum negative days in "up to X days"

2. **Example Configuration**:
   - Annual Leave: Allow Negative Cap = Yes, up to 5 days
   - Sick Leave: Allow Negative Cap = Yes, up to 3 days
   - Personal Leave: Allow Negative Cap = No

### For Employees

1. **Request Leave**:
   - When requesting leave, if negative cap is allowed, you'll see an information message
   - The system will validate your request against the negative cap limit
   - Clear error messages if limits are exceeded

## Technical Details

### Models Extended

1. **hr.leave.type**:
   - `allow_negative_cap`: Boolean field
   - `negative_cap_limit`: Float field for maximum negative days

2. **hr.leave**:
   - Enhanced validation in `_check_negative_cap_limit`
   - Related fields for easy access to negative cap settings

### Validation Logic

- If negative cap is disabled: Balance cannot go below 0
- If negative cap is enabled: Balance cannot go below -negative_cap_limit
- Validation occurs when creating/modifying leave requests

## Screenshots

The module implements the exact UI shown in your image:
- Clean "NEGATIVE CAP" section
- Toggle for "Allow Negative Cap ?"
- "up to X days" input field
- Proper spacing and alignment

## Compatibility

- Odoo 15.0
- Depends on: `hr_holidays`
- Compatible with existing leave management workflows

## Support

For support and customizations, please contact your development team.

## License

LGPL-3
